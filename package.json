{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "npm i && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@splinetool/react-spline": "^4.0.0", "@types/node": "20.6.2", "@types/nodemailer": "^6.4.17", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "framer-motion": "^11.18.2", "input-otp": "^1.2.4", "lodash": "^4.17.21", "lucide-react": "^0.446.0", "matter-js": "^0.20.0", "motion": "^12.6.2", "next": "^15.2.4", "next-themes": "^0.3.0", "nodemailer": "^6.10.0", "poly-decomp": "^0.3.0", "postcss": "^8.5.3", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "svg-path-commander": "^2.1.10", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "vaul": "^0.9.9", "zod": "^3.23.8"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/matter-js": "^0.19.8"}}