import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  const { name, email, service, message } = await request.json();

  try {
    // Log the environment variables (mask sensitive ones)
    console.log('Email User:', process.env.EMAIL_USER);
    console.log('Email Password:', process.env.EMAIL_PASSWORD ? 'Loaded' : 'Missing');

    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.zoho.com', // Use env var or default
      port: parseInt(process.env.EMAIL_PORT || '465', 10), // Use env var or default
      secure: process.env.EMAIL_SECURE === 'true', // Use env var or default
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      subject: `New Contact Form Submission!! - ${service}`,
      html: `
        <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
          <!-- Header with Logo -->
          <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
            <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
          </div>

          <!-- Main Content -->
          <div style="padding:32px 24px; background:linear-gradient(to bottom right, rgba(245,243,255,0.3), rgba(255,255,255,1));">
            <h1 style="color:#4c1d95; font-size:22px; margin:0 0 24px 0; text-align:center; font-weight:600; border-bottom:2px solid #e9d5ff; padding-bottom:12px;">New Contact Form Submission</h1>
            
            <!-- Contact Details -->
            <div style="background:#f5f3ff; padding:20px; border-radius:8px; margin-bottom:28px; border:1px solid #e9d5ff; box-shadow:0 2px 4px rgba(126,34,206,0.06);">
              <h2 style="color:#7e22ce; font-size:18px; margin:0 0 16px 0; border-left:4px solid #a855f7; padding-left:10px;">Contact Details</h2>
              <table style="width:100%; border-collapse:collapse;">
                <tr>
                  <td style="padding:10px 0; border-bottom:1px solid #e9d5ff; width:30%; color:#6b21a8; font-weight:500;">Name:</td>
                  <td style="padding:10px 0; border-bottom:1px solid #e9d5ff; color:#1e293b; font-weight:500;">${name}</td>
                </tr>
                <tr>
                  <td style="padding:10px 0; border-bottom:1px solid #e9d5ff; color:#6b21a8; font-weight:500;">Email:</td>
                  <td style="padding:10px 0; border-bottom:1px solid #e9d5ff; color:#1e293b;"><a href="mailto:${email}" style="color:#9333ea; text-decoration:none;">${email}</a></td>
                </tr>
                <tr>
                  <td style="padding:10px 0; color:#6b21a8; font-weight:500;">Service:</td>
                  <td style="padding:10px 0; color:#1e293b;"><span style="background:#e9d5ff; padding:3px 10px; border-radius:12px; font-size:14px; color:#6b21a8;">${service}</span></td>
                </tr>
              </table>
            </div>

            <!-- Message -->
            <div style="background:#f5f3ff; padding:20px; border-radius:8px; border:1px solid #e9d5ff; box-shadow:0 2px 4px rgba(126,34,206,0.06);">
              <h2 style="color:#7e22ce; font-size:18px; margin:0 0 16px 0; border-left:4px solid #a855f7; padding-left:10px;">Message</h2>
              <div style="background:white; padding:16px; border-radius:6px; color:#1e293b; line-height:1.6; border:1px solid #e9d5ff;">
                ${message || '<span style="color:#94a3b8; font-style:italic;">No message provided</span>'}
              </div>
            </div>
            
            <!-- CTA Button -->
            <div style="text-align:center; margin-top:28px;">
              <a href="mailto:${email}" style="display:inline-block; background:linear-gradient(135deg, #7e22ce, #9333ea); color:white; text-decoration:none; padding:12px 24px; border-radius:6px; font-weight:500; box-shadow:0 2px 4px rgba(126,34,206,0.2);">Reply to ${name}</a>
            </div>
          </div>

          <!-- Footer -->
          <div style="background:#f5f3ff; padding:18px; text-align:center; border-top:1px solid #e9d5ff; color:#6b21a8; font-size:13px;">
            © ${new Date().getFullYear()} UpZera. All rights reserved.
          </div>
        </div>
      `,
    };

    await transporter.sendMail(mailOptions);
    
    return NextResponse.json({ message: 'Email sent successfully' });
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
}