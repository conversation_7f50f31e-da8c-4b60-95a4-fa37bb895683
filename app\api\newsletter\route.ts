import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  const { email } = await request.json();

  // Basic validation
  if (!email || !/\S+@\S+\.\S+/.test(email)) {
    return NextResponse.json(
      { error: 'Valid email is required' },
      { status: 400 }
    );
  }

  try {
    // Create transporter using the same config as the contact form
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.zoho.com',
      port: parseInt(process.env.EMAIL_PORT || '465', 10),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    // Send notification email to admin
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      subject: 'New Newsletter Subscription',
      html: `
        <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
          <!-- Header with Logo -->
          <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
            <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
          </div>

          <!-- Main Content -->
          <div style="padding:32px 24px; background:linear-gradient(to bottom right, rgba(245,243,255,0.3), rgba(255,255,255,1));">
            <h1 style="color:#4c1d95; font-size:22px; margin:0 0 24px 0; text-align:center; font-weight:600; border-bottom:2px solid #e9d5ff; padding-bottom:12px;">New Newsletter Subscription</h1>
            
            <!-- Subscription Details -->
            <div style="background:#f5f3ff; padding:20px; border-radius:8px; margin-bottom:28px; border:1px solid #e9d5ff; box-shadow:0 2px 4px rgba(126,34,206,0.06);">
              <h2 style="color:#7e22ce; font-size:18px; margin:0 0 16px 0; border-left:4px solid #a855f7; padding-left:10px;">Subscription Details</h2>
              <table style="width:100%; border-collapse:collapse;">
                <tr>
                  <td style="padding:10px 0; color:#6b21a8; font-weight:500;">Email:</td>
                  <td style="padding:10px 0; color:#1e293b;"><a href="mailto:${email}" style="color:#9333ea; text-decoration:none;">${email}</a></td>
                </tr>
                <tr>
                  <td style="padding:10px 0; color:#6b21a8; font-weight:500;">Date:</td>
                  <td style="padding:10px 0; color:#1e293b;">${new Date().toLocaleString()}</td>
                </tr>
              </table>
            </div>
            
            <!-- CTA Button -->
            <div style="text-align:center; margin-top:28px;">
              <a href="mailto:${email}" style="display:inline-block; background:linear-gradient(135deg, #7e22ce, #9333ea); color:white; text-decoration:none; padding:12px 24px; border-radius:6px; font-weight:500; box-shadow:0 2px 4px rgba(126,34,206,0.2);">Contact Subscriber</a>
            </div>
          </div>

          <!-- Footer -->
          <div style="background:#f5f3ff; padding:18px; text-align:center; border-top:1px solid #e9d5ff; color:#6b21a8; font-size:13px;">
            © ${new Date().getFullYear()} UpZera. All rights reserved.
          </div>
        </div>
      `,
    };

    // Send confirmation email to subscriber
    const confirmationMailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Welcome to UpZera Newsletter!',
      html: `
        <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
          <!-- Header with Logo -->
          <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
            <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
          </div>

          <!-- Main Content -->
          <div style="padding:32px 24px; background:linear-gradient(to bottom right, rgba(245,243,255,0.3), rgba(255,255,255,1));">
            <h1 style="color:#4c1d95; font-size:22px; margin:0 0 24px 0; text-align:center; font-weight:600; border-bottom:2px solid #e9d5ff; padding-bottom:12px;">Welcome to Our Newsletter!</h1>
            
            <p style="color:#1e293b; line-height:1.6; margin-bottom:20px;">
              Thank you for subscribing to the UpZera newsletter. We're excited to share our latest updates, insights, and offers with you.
            </p>
            
            <p style="color:#1e293b; line-height:1.6; margin-bottom:20px;">
              You'll start receiving our newsletter at <strong>${email}</strong>. We typically send updates once a month, and you can unsubscribe at any time.
            </p>
            
            <!-- CTA Button -->
            <div style="text-align:center; margin-top:28px;">
              <a href="https://upzera.com" style="display:inline-block; background:linear-gradient(135deg, #7e22ce, #9333ea); color:white; text-decoration:none; padding:12px 24px; border-radius:6px; font-weight:500; box-shadow:0 2px 4px rgba(126,34,206,0.2);">Visit Our Website</a>
            </div>
          </div>

          <!-- Footer -->
          <div style="background:#f5f3ff; padding:18px; text-align:center; border-top:1px solid #e9d5ff; color:#6b21a8; font-size:13px;">
            © ${new Date().getFullYear()} UpZera. All rights reserved.
            <p style="margin-top:8px; font-size:11px; color:#6b21a8;">
              You're receiving this email because you signed up for our newsletter. 
              If you'd like to unsubscribe, please reply to this email with "Unsubscribe" in the subject line.
            </p>
          </div>
        </div>
      `,
    };

    // Send both emails
    await Promise.all([
      transporter.sendMail(mailOptions),
      transporter.sendMail(confirmationMailOptions)
    ]);
    
    return NextResponse.json({ message: 'Subscription successful' });
  } catch (error) {
    console.error('Error processing newsletter subscription:', error);
    return NextResponse.json(
      { error: 'Failed to process subscription' },
      { status: 500 }
    );
  }
}
